# AI费用统计修复指南

## 问题分析

管理后台数据大屏中的"AI Tokens消耗与费用统计"和"教师AI使用与费用统计"不显示费用的原因：

1. **数据源问题**：`ai_usage`集合中缺少费用相关字段（`cost`, `inputCost`, `outputCost`）
2. **定价配置问题**：`system_config`集合中可能缺少`inputPrice`和`outputPrice`字段
3. **云函数调用问题**：前端调用费用统计云函数失败或返回空数据
4. **数据同步问题**：评语数据没有同步到`ai_usage`集合

## 修复方案

### 1. 数据库层面修复

#### 步骤1：检查数据状态
```bash
# 运行调试脚本检查当前数据状态
node debug-ai-usage-data.js
```

#### 步骤2：修复定价配置
```bash
# 确保system_config集合有正确的定价配置
node upgrade-system-config-pricing.js
```

#### 步骤3：同步评语数据到ai_usage
```bash
# 如果ai_usage集合为空，同步comments数据
node sync-comments-to-ai-usage.js
```

#### 步骤4：修复费用计算
```bash
# 重新计算所有记录的费用
node fix-cost-calculation.js
```

### 2. 云函数层面修复

#### getCostStats云函数
- ✅ 已存在，功能完整
- 提供`getAllCostStats`、`getTeacherCostStats`等方法
- 直接查询`ai_usage`集合计算费用

#### adminAPI云函数
- ✅ 已配置代理到getCostStats
- 支持`cost.getAllStats`和`cost.getTeacherStats`动作

### 3. 前端层面修复

#### 修复内容
1. **增强错误处理**：添加详细的日志输出
2. **双重调用策略**：先尝试直接调用`getCostStats`，失败后通过`adminAPI`代理
3. **前端计算备用**：当云函数数据不可用时，使用前端`CostCalculator`计算
4. **数据格式兼容**：支持多种返回格式（`code: 200`或`success: true`）

#### 修改的文件
- `admin-new/src/components/AITokensChart.tsx`
- `admin-new/src/components/TeacherUsageTable.tsx`
- `admin-new/src/utils/costCalculator.ts`（新增）

### 4. 费用计算逻辑

#### 计算公式
```
输入费用 = (输入tokens数量 / 1000) × 输入定价
输出费用 = (输出tokens数量 / 1000) × 输出定价
总费用 = 输入费用 + 输出费用
```

#### 默认定价（元/千tokens）
- `doubao-pro-4k`: 输入0.00075，输出0.0030
- `doubao-pro-32k`: 输入0.001，输出0.002
- `doubao-seed-1-6-flash-250715`: 输入0.00007，输出0.0003

#### Tokens估算
- 如果没有分别记录输入输出tokens，按3:1比例估算
- 如果没有tokens数据，基于内容长度估算（中文1.5倍，英文1倍）

## 验证步骤

### 1. 运行测试脚本
```bash
node test-cost-display.js
```

### 2. 检查前端显示
1. 打开管理后台数据大屏
2. 查看"AI Tokens消耗与费用统计"卡片
3. 确认以下数据正常显示：
   - 总费用
   - 今日费用
   - 平均费用/次
   - 平均费用/千tokens
   - 今日调用次数

### 3. 检查教师统计
1. 查看"教师AI使用与费用统计"表格
2. 确认每位教师的费用数据正常显示：
   - 总费用
   - 平均费用

## 故障排除

### 问题1：ai_usage集合为空
**解决方案**：运行`sync-comments-to-ai-usage.js`同步数据

### 问题2：费用字段为0
**解决方案**：运行`fix-cost-calculation.js`重新计算费用

### 问题3：定价配置缺失
**解决方案**：运行`upgrade-system-config-pricing.js`添加定价配置

### 问题4：云函数调用失败
**解决方案**：
1. 检查云函数部署状态
2. 查看云函数日志
3. 前端会自动使用备用计算方案

### 问题5：前端显示仍为0
**解决方案**：
1. 打开浏览器开发者工具
2. 查看控制台日志
3. 确认数据获取和计算过程
4. 检查网络请求是否成功

## 监控建议

1. **定期检查**：每周运行`test-cost-display.js`验证功能
2. **数据备份**：定期备份`ai_usage`和`system_config`集合
3. **日志监控**：关注云函数调用日志和错误率
4. **费用预警**：设置费用阈值预警机制

## 技术架构

```
前端组件
├── AITokensChart.tsx (总体费用统计)
├── TeacherUsageTable.tsx (教师费用统计)
└── utils/costCalculator.ts (前端计算工具)

云函数
├── getCostStats (费用统计主函数)
├── adminAPI (管理API代理)
└── dataQuery (数据查询)

数据库
├── ai_usage (AI使用记录，包含费用字段)
├── system_config (系统配置，包含定价)
└── comments (评语数据，用于备用计算)
```

## 更新日志

- 2024-08-02：完成费用统计功能修复
- 增加前端备用计算方案
- 优化错误处理和日志输出
- 添加多重数据获取策略
